import { NextResponse } from 'next/server';
import { Resend } from 'resend';

// thf apikey
// const resend = new Resend('re_7NvihjJP_2b9wML8Pf4wKUn11WZhpicpG');

// dlp
const resend = new Resend('re_V8ETUAor_87KBNidbMUYp8oZxWD186zEK');

export async function POST() {
  try {
    const email = await resend.broadcasts.send(
      '2b3e5825-111d-467c-95b4-92a1bc0d72d9',
      {
        scheduledAt: 'Jun 2nd, 5:15PM',
      }
    );

    return NextResponse.json({
      email,
    });
  } catch (error) {
    console.error('Payment processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
