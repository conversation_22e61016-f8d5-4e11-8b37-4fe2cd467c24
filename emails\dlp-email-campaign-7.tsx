import { Column } from '@react-email/column';
import {
  Body,
  Container,
  Font,
  Head,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { Row } from '@react-email/row';

const baseUrl = 'https://img.dlp.africa/';

export const DlpMktEmail = () => (
  <Html>
    <Head>
      <style>
        {`
          * {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
          .poppins-font {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
          p, div, span, a, h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
        `}
      </style>
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecg.woff2',
          format: 'woff2',
        }}
        fontWeight={400}
        fontStyle='normal'
      />
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2',
          format: 'woff2',
        }}
        fontWeight={700}
        fontStyle='normal'
      />
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJnecmNE.woff2',
          format: 'woff2',
        }}
        fontWeight={500}
        fontStyle='normal'
      />
    </Head>
    <Preview>Evolving together in a seamlessly connected world.</Preview>
    <Body style={main} className='poppins-font'>
      {/* Header section with laptop image and welcome text */}
      <Container style={container}>
        <Section style={headerSection}>
          <Img
            src={`https://img.dlp.africa/email-campaign-7-header.png`}
            width='100%'
            height='auto'
            alt='Dlp Header Image'
            style={laptopImage}
          />
        </Section>

        {/* What you can do section */}
        <Section>
          <Text style={heroMessageTitle}>
            &apos;Ubuntu: I am because we are.&apos;
          </Text>
          <Text style={heroMessage}>- African Philosophy</Text>
        </Section>
        {/* What you can do section */}
        <Section>
          <Text style={whatYouCanDoText}>
            The rise of Africa&apos;s youth presents an unprecedented
            opportunity for collective growth and progress. DLP, with our strong{' '}
            <span style={{ color: '#c40606' }}> .africa </span>
            identity and expanding reach, is committed to partnering with both
            emerging and established businesses to navigate this exciting
            landscape.
          </Text>
          <Text style={whatYouCanDoText}>
            By providing the communication infrastructure needed to connect and
            collaborate, we can collectively thrive in this burgeoning digital
            ecosystem across the continent.
          </Text>
          <Text style={whatYouCanDoText}>
            Let&apos;s build the future of connection, together, for the benefit
            of all.
          </Text>
        </Section>
        {/* Footer */}
        <Section style={footer}>
          <Row>
            <Column style={footerLeftColumn}>
              <div style={footerLeft}>
                <Text
                  style={{
                    color: '#ffffff',
                    fontWeight: 'bold',
                    fontSize: '20px',
                  }}
                >
                  Connect & Grow
                </Text>
                <Link href='www.dlp.africa/#Services' style={websiteLink}>
                  <Text style={websiteButtonText}>Our Services</Text>
                </Link>
              </div>
            </Column>
            <Column style={footerRightColumn}>
              <div style={footerRightImageContainer}>
                <Img
                  src={`${baseUrl}/dlp-mkt-footer-001.png`}
                  width='100%'
                  height='100%'
                  alt='DLP Logo'
                  style={footerRightImage}
                />
              </div>
            </Column>
          </Row>
        </Section>

        {/* Additional Footer */}
        <Section style={additionalFooter}>
          <Section style={{ textAlign: 'center', paddingBottom: '20px' }}>
            <table style={{ width: '100%' }}>
              <tr>
                <td align='center'>
                  <Row
                    style={{
                      display: 'table-cell',
                      height: 44,
                      width: 56,
                      verticalAlign: 'bottom',
                      gap: '8px',
                    }}
                  >
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.facebook.com/delaphonegh/'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-facebook.png`}
                          width='20'
                          height='20'
                          alt='Facebook'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://x.com/DlpAfrica_'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-twitter.png`}
                          width='20'
                          height='20'
                          alt='Twitter'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.instagram.com/dlpafrica_?igsh=Y3R3ZDIyOG1uMW0z&utm_source=qr'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-instagram.png`}
                          width='20'
                          height='20'
                          alt='Instagram'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.youtube.com/channel/UCzoTHYn1QQnswn__pIuh-CQ'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-youtube.png`}
                          width='20'
                          height='20'
                          alt='YouTube'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.linkedin.com/company/dlpafrica/'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-linkedin.png`}
                          width='20'
                          height='20'
                          alt='LinkedIn'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.tiktok.com/@dlpafrica_?_t=ZS-8wVlpaNz1Rh&_r=1'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-tiktok.png`}
                          width='20'
                          height='20'
                          alt='TikTok'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                  </Row>
                </td>
              </tr>
            </table>
          </Section>
          <Text style={copyrightText}>
            © {new Date().getFullYear()} DLP Africa.
          </Text>
          <Text style={addressText}>Accra, Ghana</Text>
          <Text style={linksText}>
            <Link href='https://tally.so/r/3qeqQ5' style={footerLink}>
              Manage Subscriptions
            </Link>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default DlpMktEmail;

// Styles
const main = {
  backgroundColor: '#ffffff',
  fontFamily: '"Poppins", Verdana, Arial, sans-serif',
  color: '#333333',
};

const container = {
  margin: '0 auto',
  padding: '10px',
  width: '100%',
  height: '100%',
  maxWidth: '842px',
};

const headerSection = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '15px',
  width: '100%',
};

const laptopImage = {
  marginRight: '20px',
  alignSelf: 'flex-start' as const,
};

const heroMessageTitle = {
  fontSize: '32px',
  fontWeight: 'bold',
  color: '#c40606',
  margin: '35px 25px',
  lineHeight: '1.2',
  fontFamily: 'inherit',
  maxWidth: '600px',
  textAlign: 'left' as const,
};
const heroMessage = {
  fontSize: '18px',
  color: '#333',
  margin: '35px 25px',
  fontFamily: 'inherit',
};
const whatYouCanDoText = {
  fontSize: '14px',
  color: '#333',
  margin: '35px 25px',
  fontFamily: 'inherit',
};

const footer = {
  marginTop: '20px',
  borderTop: '1px solid #eee',
  backgroundColor: '#5b005e',
};

const footerLeftColumn = {
  width: '50%',
};

const footerRightColumn = {
  width: '50%',
};

const footerLeft = {
  textAlign: 'left' as const,
  paddingLeft: '25px' as const,
};

const websiteLink = {
  textDecoration: 'none',
  display: 'inline-block',
  marginBottom: '20px',
};

const websiteButtonText = {
  fontSize: '14px',
  fontWeight: 'bold',
  color: '#1573b7',
  backgroundColor: '#ffffff',
  padding: '4px 12px',
  borderRadius: '30px',
  margin: '0',
  textAlign: 'center' as const,
  fontFamily: 'inherit',
};

const socialIconImg = {
  margin: '0 4px',
  objectFit: 'contain' as const,
};

const footerRightImageContainer = {
  height: '100%',
  display: 'flex',
  alignItems: 'stretch',
  justifyContent: 'center',
};

const footerRightImage = {
  objectFit: 'cover' as const,
  width: '100%',
  height: '100%',
};

// Additional footer styles
const additionalFooter = {
  backgroundColor: '#f2f2f2',
  padding: '15px',
  paddingTop: '5px',
  textAlign: 'center' as const,
};

const copyrightText = {
  fontSize: '12px',
  color: '#666666',
  margin: '5px 0',
  fontFamily: 'inherit',
};

const addressText = {
  fontSize: '12px',
  color: '#666666',
  margin: '5px 0',
  fontFamily: 'inherit',
};

const linksText = {
  fontSize: '12px',
  color: '#666666',
  margin: '5px 0',
  fontFamily: 'inherit',
};

const footerLink = {
  color: '#666666',
  textDecoration: 'underline',
};
