'use client';

export default function Home() {
  async function sendMail() {
    await fetch('/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
    });
  }
  return (
    <div className='min-h-screen flex flex-col justify-center'>
      <div className='flex flex-col items-center justify-center space-y-5'>
        <div>
          <h2>Email Sender</h2>
        </div>
        <button onClick={sendMail} className='py-2 px-4 bg-black text-white'>
          Send email
        </button>
      </div>
    </div>
  );
}
