// import EMAIL from '@/emails/dlp-day-5';
import <PERSON>MA<PERSON> from '@/emails/dlp-email-campaign-7';
import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';

// thf apikey
// const resend = new Resend('re_7NvihjJP_2b9wML8Pf4wKUn11WZhpicpG');

// dlp
const resend = new Resend('re_V8ETUAor_87KBNidbMUYp8oZxWD186zEK');

// const userEmails = ['<EMAIL>'];
// const userEmails = [
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
// ];
// const dlpuserEmails = [
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
// ];
// const actualEmails = [
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',
// ];

export async function POST() {
  try {
    // Create batch emails for all users
    const rendered = await render(EMAIL());
    const text = await render(EMAIL(), {
      plainText: true,
    });

    // const c5s = `Amplifying Their Voice: DLP's Commitment to Meaningful Engagement`;
    // const c6s = `Innovating for Africa's Future: DLP's Cutting-Edge Solutions`;
    const c7s = `Partnering for Progress: DLP and Africa's Connected Future`;

    const html = await pretty(rendered);

    // const createBatchEmails = (emails: string[], subject: string) => {
    //   return emails.map((email) => ({
    //     // from: 'DLP Customer Success <<EMAIL>>',
    //     from: 'DLP Customer Success <<EMAIL>>',
    //     to: [email],
    //     subject: subject,
    //     react: EMAIL(),
    //     html: html,
    //     text: text,
    //   }));
    // };

    // // Send batch emails to all users
    // const sendEmails = await resend.batch.send(
    //   createBatchEmails(
    //     userEmails, // dlpuserEmails,
    //     c7s
    //   )
    // );

    // const teammembers = 'a5433e96-d484-4d7b-a3a2-ffa105a30286';
    // const newCustomers = '90530a44-d949-4369-b14b-139e9cd6e020';
    // const existingCustomers = '28c8f7d9-918b-44f3-b19f-71052f09da08';
    // const exCustomers = '769cb03d-9e2d-425f-9884-b2a3cf25d6cc';
    // const vendorsandpartners = '80375899-49ba-4560-a3a6-626ffca0dd23';
    // const DLPVTIGERLEADS = '2408a744-ef6d-49cb-ad93-e2bf462a239b';

    // const sendEmails = await resend.broadcasts.create({
    //   audienceId: DLPVTIGERLEADS,
    //   from: 'DLP Customer Success <<EMAIL>>',
    //   subject: c6s,
    //   react: EMAIL(),
    //   html: html,
    //   text: text,
    //   name: 'dlp-campaign-6 - DLPVTIGERLEADS',
    // });

    // DLP Audience IDS with names
    const dlpAudiences = [
      {
        id: 'a5433e96-d484-4d7b-a3a2-ffa105a30286',
        name: 'teammembers',
      },
      {
        id: '90530a44-d949-4369-b14b-139e9cd6e020',
        name: 'newCustomers',
      },
      {
        id: '28c8f7d9-918b-44f3-b19f-71052f09da08',
        name: 'existingCustomers',
      },
      {
        id: '769cb03d-9e2d-425f-9884-b2a3cf25d6cc',
        name: 'exCustomers',
      },
      {
        id: '80375899-49ba-4560-a3a6-626ffca0dd23',
        name: 'vendorsandpartners',
      },
      {
        id: '011432ee-b2af-4144-9ccf-41f7d099a5b2',
        name: 'test',
      },
      {
        id: '2408a744-ef6d-49cb-ad93-e2bf462a239b',
        name: 'DLPVTIGERLEADS',
      },
    ];

    // Send emails to all audiences
    const sendEmailsResults = [];

    for (const audience of dlpAudiences) {
      try {
        const sendEmails = await resend.broadcasts.create({
          audienceId: audience.id,
          from: 'DLP Customer Success <<EMAIL>>',
          subject: c7s,
          react: EMAIL(),
          html: html,
          text: text,
          name: `dlp-campaign-7 - ${audience.name}`,
        });

        sendEmailsResults.push({
          audience: audience.name,
          audienceId: audience.id,
          result: sendEmails,
          status: 'success',
        });
      } catch (error) {
        console.error(`Error sending to audience ${audience.name}:`, error);
        sendEmailsResults.push({
          audience: audience.name,
          audienceId: audience.id,
          error: error,
          status: 'failed',
        });
      }
    }

    // return NextResponse.json({
    //   message: `Sent emails to ${sendEmails.data} audiences`,
    //   results: sendEmails,
    // });
    return NextResponse.json({
      message: `Sent emails to ${sendEmailsResults.length} audiences`,
      results: sendEmailsResults,
    });
  } catch (error) {
    console.error('Payment processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
