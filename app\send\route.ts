// import EMAIL from '@/emails/dlp-day-5';
import <PERSON>MA<PERSON> from '@/emails/dlp-email-campaign-5';
import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';

// thf apikey
// const resend = new Resend('re_7NvihjJP_2b9wML8Pf4wKUn11WZhpicpG');

// dlp
const resend = new Resend('re_V8ETUAor_87KBNidbMUYp8oZxWD186zEK');

// const userEmails = ['<EMAIL>'];
// const userEmails = [
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
// ];
// const dlpuserEmails = [
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
// ];
// const actualEmails = [
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',
//   '<EMAIL>',

//   '<EMAIL>',

//   '<EMAIL>',
// ];

export async function POST() {
  try {
    // Create batch emails for all users
    const rendered = await render(EMAIL());
    const text = await render(EMAIL(), {
      plainText: true,
    });

    const c5s = `Amplifying Their Voice: DLP's Commitment to Meaningful Engagement`;
    // const c6s = `Innovating for Africa's Future: DLP's Cutting-Edge Solutions`;
    // const c7s = `Partnering for Progress: DLP and Africa's Connected Future`;

    const html = await pretty(rendered);

    // const createBatchEmails = (emails: string[], subject: string) => {
    //   return emails.map((email) => ({
    //     // from: 'DLP Customer Success <<EMAIL>>',
    //     from: 'DLP Customer Success <<EMAIL>>',
    //     to: [email],
    //     subject: subject,
    //     react: EMAIL(),
    //     html: html,
    //     text: text,
    //   }));
    // };

    // // Send batch emails to all users
    // const sendEmails = await resend.batch.send(
    //   createBatchEmails(
    //     userEmails, // dlpuserEmails,
    //     c7s
    //   )
    // );

    // DLP Audience IDS
    const teammembers = 'a5433e96-d484-4d7b-a3a2-ffa105a30286';
    // const newCustomers = '90530a44-d949-4369-b14b-139e9cd6e020';
    // const existingCustomers = '28c8f7d9-918b-44f3-b19f-71052f09da08';
    // const exCustomers = '769cb03d-9e2d-425f-9884-b2a3cf25d6cc';
    // const vendorsandpartners = '80375899-49ba-4560-a3a6-626ffca0dd23';
    // const DLPVTIGERLEADS = '2408a744-ef6d-49cb-ad93-e2bf462a239b';

    const sendEmails = await resend.broadcasts.create({
      audienceId: teammembers,
      from: 'DLP Customer Success <<EMAIL>>',
      subject: c5s,
      react: EMAIL(),
      html: html,
      text: text,
      name: 'dlp-campaign-5 - teammembers',
    });

    return NextResponse.json({ sendEmails });
  } catch (error) {
    console.error('Payment processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
