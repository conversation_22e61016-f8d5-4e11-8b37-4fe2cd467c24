import {
  Body,
  Container,
  Font,
  Head,
  Html,
  Img,
  Preview,
  Section,
} from '@react-email/components';

export const EmailCampaign3 = () => (
  <Html>
    <Head>
      <style>
        {`
          * {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
          .poppins-font {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
          p, div, span, a, h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
         
        `}
      </style>
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecg.woff2',
          format: 'woff2',
        }}
        fontWeight={400}
        fontStyle='normal'
      />
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2',
          format: 'woff2',
        }}
        fontWeight={700}
        fontStyle='normal'
      />
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJnecmNE.woff2',
          format: 'woff2',
        }}
        fontWeight={500}
        fontStyle='normal'
      />
    </Head>
    <Preview>Envisioning a world seamlessly connected.</Preview>
    <Body style={main} className='poppins-font'>
      {/* Header section with laptop image and welcome text */}
      <Container style={container}>
        <Section style={headerSection}>
          <Img
            src={`https://img.dlp.africa/email-campaign-3.png`}
            width='100%'
            height='auto'
            alt='Dlp Header Image'
            style={laptopImage}
          />
        </Section>
      </Container>
    </Body>
  </Html>
);

export default EmailCampaign3;

// Styles
const main = {
  backgroundColor: '#ffffff',
  fontFamily: '"Poppins", Verdana, Arial, sans-serif',
  color: '#333333',
};

const container = {
  margin: '0 auto',
  padding: '10px',
  width: '100%',
  height: '100%',
  maxWidth: '842px',
};

const headerSection = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '15px',
  width: '100%',
};

const laptopImage = {
  marginRight: '20px',
  alignSelf: 'flex-start' as const,
};
