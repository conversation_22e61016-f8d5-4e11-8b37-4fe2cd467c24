import { Column } from '@react-email/column';
import {
  Body,
  Container,
  <PERSON>ont,
  Head,
  Html,
  Img,
  Link,
  // Preview,
  Section,
  Text,
} from '@react-email/components';
import { Row } from '@react-email/row';

const baseUrl = 'https://img.dlp.africa/';

export const DlpDay5 = () => (
  <Html>
    <Head>
      <style>
        {`
          * {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
          .poppins-font {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }
          p, div, span, a, h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', Verdana, Arial, sans-serif !important;
          }

          @media only screen and (max-width: 399px) {
            .responsive-row {
              display: block !important;
              width: 100% !important;
            }
            .responsive-column {
              display: block !important;
              width: 100% !important;
              padding: 0 !important;
              margin-bottom: 15px !important;
            }
            .responsive-image-column {
              display: block !important;
              width: 100% !important;
              padding: 0 !important;
              margin-bottom: 15px !important;
              text-align: center !important;
            }
            .responsive-text-column {
              display: block !important;
              width: 100% !important;
              padding: 0 !important;
            }
            .responsive-footer {
              min-height: 130px !important;
            }
          }
        `}
      </style>
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfecg.woff2',
          format: 'woff2',
        }}
        fontWeight={400}
        fontStyle='normal'
      />
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2',
          format: 'woff2',
        }}
        fontWeight={700}
        fontStyle='normal'
      />
      <Font
        fontFamily='Poppins'
        fallbackFontFamily={['Verdana', 'Arial', 'sans-serif']}
        webFont={{
          url: 'https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJnecmNE.woff2',
          format: 'woff2',
        }}
        fontWeight={500}
        fontStyle='normal'
      />
    </Head>
    {/* <Preview>Experience our brand&apos;s refreshed energy..</Preview> */}
    <Body style={main} className='poppins-font'>
      {/* Header section with laptop image and welcome text */}
      <Container style={container}>
        <Section style={headerSection}>
          <Img
            src={`${baseUrl}/day-5-announcement-header.png`}
            width='100%'
            height='auto'
            alt='Dlp Header Image'
            style={laptopImage}
          />
        </Section>

        {/* What you can do section */}
        <Section>
          <Text style={whatYouCanDoText}>
            We&apos;ve evolved with a fresh name, a sleek new look, and even
            more powerful solutions to help you connect, grow, and lead across
            Africa.
          </Text>
        </Section>
        {/* What you can do section */}
        <Section>
          <Text style={heroMessageTitle}>What&apos;s new at DLP?</Text>
          <div style={checklistItem}>
            <span style={checkmark}>•</span>
            <Text style={checklistText}>A revamped user-friendly website</Text>
          </div>
          <div style={checklistItem}>
            <span style={checkmark}>•</span>
            <Text style={checklistText}>
              Faster, smarter service dashboards
            </Text>
          </div>
          <div style={checklistItem}>
            <span style={checkmark}>•</span>
            <Text style={checklistText}>
              Simplified access to bulk SMS, voice messaging & data bundles
            </Text>
          </div>
          <div style={checklistItem}>
            <span style={checkmark}>•</span>
            <Text style={checklistText}>
              Exciting new features tailored for businesses like yours
            </Text>
          </div>
          <Text style={whatYouCanDoText}>But That&apos;s not all, ...</Text>
        </Section>

        {/* What you can do section */}
        <Section
          style={{
            marginTop: '30px',
            marginBottom: '8px',
          }}
        >
          <Row className='responsive-row'>
            <Column
              className='responsive-image-column'
              style={{ width: '40%', paddingRight: '15px' }}
            >
              <Img
                src={`${baseUrl}/introducing-fish-africa.png`}
                width='100%'
                height='auto'
                alt='Introducing Fish Africa'
              />
            </Column>
            <Column className='responsive-text-column' style={{ width: '60%' }}>
              <Text
                style={{
                  ...whatYouCanDoText,
                }}
              >
                Your all-in-one{' '}
                <span style={{ color: '#0078C8', fontWeight: 'bold' }}>
                  Communication Platform as a Service (CPaaS)
                </span>
                . Fish Africa empowers your business to{' '}
                <span style={{ color: '#0078C8', fontWeight: 'bold' }}>
                  engage
                </span>
                ,{' '}
                <span style={{ color: '#0078C8', fontWeight: 'bold' }}>
                  automate
                </span>
                , and{' '}
                <span style={{ color: '#0078C8', fontWeight: 'bold' }}>
                  scale
                </span>{' '}
                communication effortlessly all across the continent. Connect
                instantly with SMS, Voice Messages, Airtime and Data top-ups-all
                from one platform.
              </Text>
            </Column>
          </Row>
          <Text style={whatYouCanDoText}>
            And because we believe growth should come with great surprises,
            we&apos;ve lined up amazing gifts for our early users and social
            media followers:
          </Text>
          {/* Benefits list */}
          <div style={benefitsContainer}>
            <div style={benefitItem}>
              <span style={benefitBullet}>■</span>
              <Text style={benefitText}>Free data</Text>
            </div>
            <div style={benefitItem}>
              <span style={benefitBullet}>■</span>
              <Text style={benefitText}>Free bulk SMS</Text>
            </div>
            <div style={benefitItem}>
              <span style={benefitBullet}>■</span>
              <Text style={benefitText}>Free voice messages</Text>
            </div>
          </div>
          <Text style={whatYouCanDoText}>
            Get started today and experience a new wave of communication across
            Africa!
          </Text>
          <Text style={whatYouCanDoText}>
            Thank you for being part of our journey. We&apos;re just getting
            started.
          </Text>
        </Section>
        {/* Footer */}
        <Section className='responsive-footer' style={newFooter}>
          <div style={footerContent}>
            <div style={footerLeftSection}>
              <div>
                <Text
                  style={{
                    color: '#ffffff',
                    fontWeight: 'Bold',
                    // marginLeft: '10px',
                  }}
                >
                  See Our Look!
                </Text>
                <Link href='https://www.dlp.africa' style={websiteLink}>
                  <Text style={websiteButtonText}>www.dlp.africa</Text>
                </Link>
              </div>
            </div>
            <div style={footerRightSection}></div>
          </div>
        </Section>

        {/* Additional Footer */}
        <Section style={additionalFooter}>
          <Section style={{ textAlign: 'center', paddingBottom: '20px' }}>
            <table style={{ width: '100%' }}>
              <tr>
                <td align='center'>
                  <Row
                    style={{
                      display: 'table-cell',
                      height: 44,
                      width: 56,
                      verticalAlign: 'bottom',
                      gap: '8px',
                    }}
                  >
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.facebook.com/delaphonegh/'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-facebook.png`}
                          width='20'
                          height='20'
                          alt='Facebook'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://x.com/DlpAfrica_'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-twitter.png`}
                          width='20'
                          height='20'
                          alt='Twitter'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.instagram.com/dlpafrica_?igsh=Y3R3ZDIyOG1uMW0z&utm_source=qr'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-instagram.png`}
                          width='20'
                          height='20'
                          alt='Instagram'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://youtube.com/@delaphonegh?si=Jut0YxP66zYiHt15'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-youtube.png`}
                          width='20'
                          height='20'
                          alt='YouTube'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.linkedin.com/company/dlpafrica/'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-linkedin.png`}
                          width='20'
                          height='20'
                          alt='LinkedIn'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                    <Column style={{ paddingRight: 8 }}>
                      <Link href='https://www.tiktok.com/@dlpafrica_?_t=ZS-8wVlpaNz1Rh&_r=1'>
                        <Img
                          src={`${baseUrl}/icons/blue/social-icon-tiktok.png`}
                          width='20'
                          height='20'
                          alt='TikTok'
                          style={socialIconImg}
                        />
                      </Link>
                    </Column>
                  </Row>
                </td>
              </tr>
            </table>
          </Section>
          <Text style={copyrightText}>
            © {new Date().getFullYear()} DLP Africa.
          </Text>
          <Text style={addressText}>
            3 Numo Kpabi Kpabi St, Tse Addo, Accra, Ghana
          </Text>
          <Text style={linksText}>
            <Link href='https://tally.so/r/3qeqQ5' style={footerLink}>
              Manage Subscriptions
            </Link>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default DlpDay5;

// Styles
const main = {
  backgroundColor: '#ffffff',
  fontFamily: '"Poppins", Verdana, Arial, sans-serif',
  color: '#333333',
};

const container = {
  margin: '0 auto',
  padding: '10px',
  width: '100%',
  height: '100%',
  maxWidth: '842px',
};

const headerSection = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '0px',
  width: '100%',
};

const laptopImage = {
  marginRight: '20px',
  alignSelf: 'flex-start' as const,
};

const heroMessageTitle = {
  fontSize: '24px',
  fontWeight: 'bold',
  color: '#66666',
  margin: '8px 25px',
  lineHeight: '1.2',
  fontFamily: 'inherit',
  maxWidth: '600px',
  textAlign: 'left' as const,
};

const whatYouCanDoText = {
  fontSize: '14px',
  color: '#333',
  margin: '35px 25px',
  fontFamily: 'inherit',
};

const checklistItem = {
  display: 'flex',
  alignItems: 'baseline',
  marginBottom: '2px',
  margin: '0px 25px',
};

const checkmark = {
  color: '#0078C8',
  marginRight: '8px',
  fontWeight: 'bold',
  flexShrink: 0,
  lineHeight: '1.4',
};

const checklistText = {
  fontSize: '14px',
  color: '#333',
  margin: '4px 0',
  lineHeight: '1.4',
};

const websiteLink = {
  textDecoration: 'none',
  // display: 'inline-block',
  marginBottom: '20px',
};

const websiteButtonText = {
  fontSize: '16px',
  fontWeight: 'bold',
  color: '#1573b7',
  backgroundColor: '#ffffff',
  padding: '4px 12px',
  borderRadius: '30px',
  margin: '0',
  textAlign: 'center' as const,
  fontFamily: 'inherit',
};

const socialIconImg = {
  margin: '0 4px',
  objectFit: 'contain' as const,
};

// Additional footer styles
const additionalFooter = {
  backgroundColor: '#f2f2f2',
  padding: '15px',
  paddingTop: '5px',
  textAlign: 'center' as const,
};

const copyrightText = {
  fontSize: '12px',
  color: '#666666',
  margin: '5px 0',
  fontFamily: 'inherit',
};

const addressText = {
  fontSize: '12px',
  color: '#666666',
  margin: '5px 0',
  fontFamily: 'inherit',
};

const linksText = {
  fontSize: '12px',
  color: '#666666',
  margin: '5px 0',
  fontFamily: 'inherit',
};

const footerLink = {
  color: '#666666',
  textDecoration: 'underline',
};

// Benefits section styles
const benefitsContainer = {
  display: 'flex',
  flexDirection: 'row' as const,
  justifyContent: 'space-around',
  alignItems: 'center',
  margin: '20px 25px',
  flexWrap: 'wrap' as const,
};

const benefitItem = {
  display: 'flex',
  alignItems: 'center',
  marginBottom: '10px',
  flex: '1',
  minWidth: '150px',
};

const benefitBullet = {
  color: '#0078C8',
  marginRight: '8px',
  fontWeight: 'bold',
  fontSize: '16px',
};

const benefitText = {
  fontSize: '14px',
  color: '#0078C8',
  fontWeight: 'bold',
  margin: '0',
  fontFamily: 'inherit',
};

// New footer styles
const newFooter = {
  marginTop: '20px',
  backgroundImage: `url(${baseUrl}/day5-announcement-footer.png)`,
  backgroundSize: '100% auto',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  padding: '40px 20px',
  minHeight: '330px',
};

const footerContent = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  alignContent: 'center',
  maxWidth: '800px',
  margin: '0 auto',
  width: '100%',
};

const footerLeftSection = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'flex-start',
};

const footerRightSection = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'flex-end',
  textAlign: 'right' as const,
};
